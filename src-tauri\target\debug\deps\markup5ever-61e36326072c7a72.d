D:\github\projects\Voice Input\src-tauri\target\debug\deps\markup5ever-61e36326072c7a72.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs D:\github\projects\Voice\ Input\src-tauri\target\debug\build\markup5ever-fd956a99c8670dbf\out/generated.rs D:\github\projects\Voice\ Input\src-tauri\target\debug\build\markup5ever-fd956a99c8670dbf\out/named_entities.rs

D:\github\projects\Voice Input\src-tauri\target\debug\deps\libmarkup5ever-61e36326072c7a72.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs D:\github\projects\Voice\ Input\src-tauri\target\debug\build\markup5ever-fd956a99c8670dbf\out/generated.rs D:\github\projects\Voice\ Input\src-tauri\target\debug\build\markup5ever-fd956a99c8670dbf\out/named_entities.rs

D:\github\projects\Voice Input\src-tauri\target\debug\deps\libmarkup5ever-61e36326072c7a72.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs D:\github\projects\Voice\ Input\src-tauri\target\debug\build\markup5ever-fd956a99c8670dbf\out/generated.rs D:\github\projects\Voice\ Input\src-tauri\target\debug\build\markup5ever-fd956a99c8670dbf\out/named_entities.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs:
D:\github\projects\Voice\ Input\src-tauri\target\debug\build\markup5ever-fd956a99c8670dbf\out/generated.rs:
D:\github\projects\Voice\ Input\src-tauri\target\debug\build\markup5ever-fd956a99c8670dbf\out/named_entities.rs:

# env-dep:OUT_DIR=D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\markup5ever-fd956a99c8670dbf\\out
