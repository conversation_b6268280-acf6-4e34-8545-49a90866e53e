{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12092653563678505622, "build_script_build", false, 3577928146634899441], [16702348383442838006, "build_script_build", false, 6323856960564521336], [1854381179048223083, "build_script_build", false, 6501032835509193220]], "local": [{"RerunIfChanged": {"output": "debug\\build\\voice-input-da39babd33a7d1be\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}