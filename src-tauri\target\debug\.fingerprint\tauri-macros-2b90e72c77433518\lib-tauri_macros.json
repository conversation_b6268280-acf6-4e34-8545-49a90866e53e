{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 4801497374888428737, "deps": [[654232091421095663, "tauri_utils", false, 15616659188895448736], [737309000288863258, "proc_macro2", false, 10381056870723521652], [2704937418414716471, "tauri_codegen", false, 5660058716773447663], [4974441333307933176, "syn", false, 712804826197487993], [13077543566650298139, "heck", false, 2310260232102032935], [17990358020177143287, "quote", false, 3509695914107320920]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-2b90e72c77433518\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}