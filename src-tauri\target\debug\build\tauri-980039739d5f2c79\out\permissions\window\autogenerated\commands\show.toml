# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-show"
description = "Enables the show command without any pre-configured scope."
commands.allow = ["show"]

[[permission]]
identifier = "deny-show"
description = "Denies the show command without any pre-configured scope."
commands.deny = ["show"]
