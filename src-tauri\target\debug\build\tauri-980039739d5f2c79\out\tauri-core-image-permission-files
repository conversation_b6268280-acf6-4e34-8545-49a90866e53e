["\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\image\\autogenerated\\commands\\from_bytes.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\image\\autogenerated\\commands\\from_path.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\image\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\image\\autogenerated\\commands\\rgba.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\image\\autogenerated\\commands\\size.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\image\\autogenerated\\default.toml"]