# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-listen"
description = "Enables the listen command without any pre-configured scope."
commands.allow = ["listen"]

[[permission]]
identifier = "deny-listen"
description = "Denies the listen command without any pre-configured scope."
commands.deny = ["listen"]
