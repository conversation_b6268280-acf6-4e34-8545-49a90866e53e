{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2241668132362809309, "path": 8979343188923196492, "deps": [[376837177317575824, "build_script_build", false, 15068495297167201311], [4143744114649553716, "raw_window_handle", false, 126447740291223225], [5986029879202738730, "log", false, 17103523495733528716], [10281541584571964250, "windows_sys", false, 8904757422217441915]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-8522cab0673b0cc8\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}