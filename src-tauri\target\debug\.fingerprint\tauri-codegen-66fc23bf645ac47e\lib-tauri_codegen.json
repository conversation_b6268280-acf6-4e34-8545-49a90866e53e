{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 8894278055034350775, "deps": [[654232091421095663, "tauri_utils", false, 15616659188895448736], [737309000288863258, "proc_macro2", false, 10381056870723521652], [3150220818285335163, "url", false, 17961956666348065251], [4899080583175475170, "semver", false, 7468540637895916940], [4974441333307933176, "syn", false, 712804826197487993], [7170110829644101142, "json_patch", false, 17431485384700290681], [7392050791754369441, "ico", false, 17605265302326925008], [8319709847752024821, "uuid", false, 9489781470082133488], [9556762810601084293, "brotli", false, 1612878063969801434], [9689903380558560274, "serde", false, 14423749025213562542], [9857275760291862238, "sha2", false, 4301780009647179487], [10806645703491011684, "thiserror", false, 13254882049126059903], [12687914511023397207, "png", false, 10576303033918697383], [13077212702700853852, "base64", false, 14787360010863829238], [15622660310229662834, "walkdir", false, 13141832595847327072], [16362055519698394275, "serde_json", false, 2334346187745280281], [17990358020177143287, "quote", false, 3509695914107320920]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-66fc23bf645ac47e\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}