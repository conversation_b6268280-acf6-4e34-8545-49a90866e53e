{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 11377959802098117882, "deps": [[3150220818285335163, "url", false, 17961956666348065251], [6913375703034175521, "build_script_build", false, 3984175276926118500], [6982418085031928086, "dyn_clone", false, 10314649709934708706], [8319709847752024821, "uuid1", false, 9489781470082133488], [9689903380558560274, "serde", false, 14423749025213562542], [14923790796823607459, "indexmap", false, 1052336392275449608], [16071897500792579091, "schemars_derive", false, 4488734422333338572], [16362055519698394275, "serde_json", false, 2334346187745280281]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-6a27101eed9a50f4\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}