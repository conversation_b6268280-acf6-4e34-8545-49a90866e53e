cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=D:\github\projects\Voice Input\src-tauri\tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=voice_input
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_tauri_1app
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=D:\github\projects\Voice Input\src-tauri\target\debug\build\voice-input-da39babd33a7d1be\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
TOML parsing error
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=D:\github\projects\Voice Input\src-tauri\target\debug\build\voice-input-da39babd33a7d1be\out\resource.lib
