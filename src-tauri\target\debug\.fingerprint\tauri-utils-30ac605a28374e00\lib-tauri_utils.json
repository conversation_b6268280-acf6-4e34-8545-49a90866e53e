{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 83139694899628915, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 1581554239249405756], [3150220818285335163, "url", false, 2710635927360323827], [3191507132440681679, "serde_untagged", false, 12437894862631463741], [4071963112282141418, "serde_with", false, 8113046627677992023], [4899080583175475170, "semver", false, 16197991607841655478], [5986029879202738730, "log", false, 17103523495733528716], [6606131838865521726, "ctor", false, 10052892399873653899], [7170110829644101142, "json_patch", false, 10996252316665988116], [8319709847752024821, "uuid", false, 5882731122990469284], [9010263965687315507, "http", false, 17046921390355211077], [9451456094439810778, "regex", false, 8035670133280570470], [9556762810601084293, "brotli", false, 5646480653990378443], [9689903380558560274, "serde", false, 12081238993034187673], [10806645703491011684, "thiserror", false, 925641633585609860], [11989259058781683633, "dunce", false, 11560442118751391268], [13625485746686963219, "anyhow", false, 4759897712641697178], [15609422047640926750, "toml", false, 17375328135183683347], [15622660310229662834, "walkdir", false, 11249427753159787704], [15932120279885307830, "memchr", false, 1811853779724236704], [16362055519698394275, "serde_json", false, 6401569572804738106], [17146114186171651583, "infer", false, 10672211361788542974], [17155886227862585100, "glob", false, 1268829269090242564], [17186037756130803222, "phf", false, 6123169683725688297]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-30ac605a28374e00\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}