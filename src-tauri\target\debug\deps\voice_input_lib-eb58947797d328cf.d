D:\github\projects\Voice Input\src-tauri\target\debug\deps\voice_input_lib-eb58947797d328cf.d: src\lib.rs D:\github\projects\Voice\ Input\src-tauri\target\debug\build\voice-input-da39babd33a7d1be\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\github\projects\Voice Input\src-tauri\target\debug\deps\libvoice_input_lib-eb58947797d328cf.rmeta: src\lib.rs D:\github\projects\Voice\ Input\src-tauri\target\debug\build\voice-input-da39babd33a7d1be\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\lib.rs:
D:\github\projects\Voice\ Input\src-tauri\target\debug\build\voice-input-da39babd33a7d1be\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=voice-input
# env-dep:OUT_DIR=D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\voice-input-da39babd33a7d1be\\out
