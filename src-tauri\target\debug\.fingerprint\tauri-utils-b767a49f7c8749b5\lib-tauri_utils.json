{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 83139694899628915, "deps": [[737309000288863258, "proc_macro2", false, 10381056870723521652], [1200537532907108615, "url<PERSON><PERSON>n", false, 12846578214241368870], [3150220818285335163, "url", false, 17961956666348065251], [3191507132440681679, "serde_untagged", false, 9510374609948571555], [4071963112282141418, "serde_with", false, 9940240462411292084], [4899080583175475170, "semver", false, 7468540637895916940], [5986029879202738730, "log", false, 14519990131422226924], [6606131838865521726, "ctor", false, 10052892399873653899], [6913375703034175521, "schemars", false, 13304064352621375301], [7170110829644101142, "json_patch", false, 17431485384700290681], [8319709847752024821, "uuid", false, 9489781470082133488], [9010263965687315507, "http", false, 2836004348922963081], [9451456094439810778, "regex", false, 13300283371604750100], [9556762810601084293, "brotli", false, 1612878063969801434], [9689903380558560274, "serde", false, 14423749025213562542], [10806645703491011684, "thiserror", false, 13254882049126059903], [11655476559277113544, "cargo_metadata", false, 3338982413480447140], [11989259058781683633, "dunce", false, 16057321962837055627], [13625485746686963219, "anyhow", false, 9613096878410341271], [14232843520438415263, "html5ever", false, 8874008025412643012], [15088007382495681292, "kuchiki", false, 7405051783082847418], [15609422047640926750, "toml", false, 9326942548471378759], [15622660310229662834, "walkdir", false, 13141832595847327072], [15932120279885307830, "memchr", false, 446529596446114209], [16362055519698394275, "serde_json", false, 2334346187745280281], [17146114186171651583, "infer", false, 4945250198137869463], [17155886227862585100, "glob", false, 12107979883393210905], [17186037756130803222, "phf", false, 7652078111637995100], [17990358020177143287, "quote", false, 3509695914107320920]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-b767a49f7c8749b5\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}