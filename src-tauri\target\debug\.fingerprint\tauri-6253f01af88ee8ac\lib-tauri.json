{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 6335087436104091831, "deps": [[40386456601120721, "percent_encoding", false, 11451824362267699693], [654232091421095663, "tauri_utils", false, 8021918626345510030], [1200537532907108615, "url<PERSON><PERSON>n", false, 1581554239249405756], [1967864351173319501, "muda", false, 3200696248486876102], [2013030631243296465, "webview2_com", false, 8960281004288357200], [3150220818285335163, "url", false, 2710635927360323827], [3331586631144870129, "getrandom", false, 4528896524885983854], [4143744114649553716, "raw_window_handle", false, 126447740291223225], [4919829919303820331, "serialize_to_javascript", false, 14966819216518332054], [5986029879202738730, "log", false, 17103523495733528716], [9010263965687315507, "http", false, 17046921390355211077], [9689903380558560274, "serde", false, 12081238993034187673], [10229185211513642314, "mime", false, 15459723414947441071], [10806645703491011684, "thiserror", false, 925641633585609860], [11989259058781683633, "dunce", false, 11560442118751391268], [12092653563678505622, "build_script_build", false, 3577928146634899441], [12304025191202589669, "tauri_runtime_wry", false, 11214167210956758794], [12565293087094287914, "window_vibrancy", false, 10109244614685600910], [12943761728066819757, "tauri_runtime", false, 2982704078931448790], [12986574360607194341, "serde_repr", false, 17708761034626888411], [13077543566650298139, "heck", false, 977132307339587524], [13405681745520956630, "tauri_macros", false, 2476903364351215015], [13625485746686963219, "anyhow", false, 4759897712641697178], [14585479307175734061, "windows", false, 10085425527849788877], [16362055519698394275, "serde_json", false, 6401569572804738106], [16928111194414003569, "dirs", false, 12732796372557786993], [17155886227862585100, "glob", false, 1268829269090242564], [17531218394775549125, "tokio", false, 13004517618062702429]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-6253f01af88ee8ac\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}