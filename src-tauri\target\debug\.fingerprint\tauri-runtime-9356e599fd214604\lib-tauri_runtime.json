{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 48225779667954856, "deps": [[654232091421095663, "tauri_utils", false, 8021918626345510030], [3150220818285335163, "url", false, 2710635927360323827], [4143744114649553716, "raw_window_handle", false, 126447740291223225], [7606335748176206944, "dpi", false, 9973306581245937981], [9010263965687315507, "http", false, 17046921390355211077], [9689903380558560274, "serde", false, 12081238993034187673], [10806645703491011684, "thiserror", false, 925641633585609860], [12943761728066819757, "build_script_build", false, 15488795224673834313], [14585479307175734061, "windows", false, 10085425527849788877], [16362055519698394275, "serde_json", false, 6401569572804738106], [16727543399706004146, "cookie", false, 5768184357568065378]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-9356e599fd214604\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}