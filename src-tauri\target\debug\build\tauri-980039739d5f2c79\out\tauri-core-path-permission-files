["\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\github\\projects\\Voice Input\\src-tauri\\target\\debug\\build\\tauri-980039739d5f2c79\\out\\permissions\\path\\autogenerated\\default.toml"]