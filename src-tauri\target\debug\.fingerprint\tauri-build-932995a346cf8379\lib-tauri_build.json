{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 19569072800467306, "deps": [[654232091421095663, "tauri_utils", false, 15616659188895448736], [4824857623768494398, "cargo_toml", false, 15334613418746248453], [4899080583175475170, "semver", false, 7468540637895916940], [5165059047667588304, "tauri_winres", false, 8159904113228782039], [6913375703034175521, "schemars", false, 13304064352621375301], [7170110829644101142, "json_patch", false, 17431485384700290681], [9689903380558560274, "serde", false, 14423749025213562542], [13077543566650298139, "heck", false, 2310260232102032935], [13625485746686963219, "anyhow", false, 9613096878410341271], [15609422047640926750, "toml", false, 9326942548471378759], [15622660310229662834, "walkdir", false, 13141832595847327072], [16362055519698394275, "serde_json", false, 2334346187745280281], [16928111194414003569, "dirs", false, 9629153185681952333], [17155886227862585100, "glob", false, 12107979883393210905]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-932995a346cf8379\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}