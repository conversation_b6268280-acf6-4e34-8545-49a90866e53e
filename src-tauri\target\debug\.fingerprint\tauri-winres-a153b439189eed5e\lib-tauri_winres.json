{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 2086567024422996381, "profile": 2225463790103693989, "path": 15953272831349706717, "deps": [[6941104557053927479, "embed_resource", false, 2314253768852066317], [12060164242600251039, "toml", false, 1147080757576887682]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winres-a153b439189eed5e\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}